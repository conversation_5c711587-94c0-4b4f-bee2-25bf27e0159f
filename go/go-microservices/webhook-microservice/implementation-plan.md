# Webhook Microservice - Phased Implementation Plan

This implementation plan outlines each step of building the webhook microservice according to Domain-Driven Design (DDD) principles. It builds incrementally, each phase being testable with clear acceptance criteria.

---

## ✅ Phase 1: Project Skeleton & Configuration

**Goal:** Establish project structure, load configuration from YAML, and bootstrap the server.

### Tasks:
- Scaffold folders and files based on agreed structure.
- Create `config.yaml` and `internal/config/config.go` to load values.
- Build `cmd/server/main.go` to load config and start a minimal Gin server.

### Acceptance Criteria:
- `run-local.sh` starts the Gin server at configured port.
- Server responds to `GET /healthz` with `200 OK`.
- Config is loaded from `configs/config.yaml`.
- Logs show loaded values.

---

## ✅ Phase 2: API Layer & Middleware Setup

**Goal:** Implement basic HTTP routing, WAF, and validation middleware.

### Tasks:
- Build `internal/api/router.go` with `POST /webhook`.
- Add `middleware/validator.go`:
  - Check headers `X-Delivery-ID`, `X-Signature`.
- Add `middleware/waf.go`:
  - Allowlist IPs.
  - Rate-limit per IP using in-memory/bucket4j logic.

### Acceptance Criteria:
- Requests missing headers return `400 Bad Request`.
- Blocked IPs return `403 Forbidden`.
- Valid requests reach a dummy handler.
- Unit tests for both middleware components.

---

## ✅ Phase 3: Domain Model

**Goal:** Implement `WebhookEvent` and supporting value objects.

### Tasks:
- Create `internal/domain/webhook.go`:
  - `WebhookEvent`, `DeliveryID`, `ReceivedAt`.
- Add construction & validation logic (e.g., empty ID error).

### Acceptance Criteria:
- Value object creation is tested with invalid/valid inputs.
- Test webhook aggregate correctly instantiates with all fields.

---

## ✅ Phase 4: Command Handler

**Goal:** Implement the application service that validates, deduplicates, and publishes.

### Tasks:
- Define `HandleWebhookCommand` in `internal/app/commands/handle_webhook.go`.
- Implement domain-level `HandleWebhook(...)` logic.
- Stub `DeduplicationService` and `PublishingService` interfaces.

### Acceptance Criteria:
- Command handler can be tested in isolation.
- Errors: `ErrInvalidID`, `ErrDuplicate`, `ErrPublishFailed` are returned correctly.

---

## ✅ Phase 5: Infrastructure Layer – Dapr Adapters

**Goal:** Connect domain ports to Dapr state store and pub/sub.

### Tasks:
- `state_client.go`: Implements `IsDuplicate`, `MarkProcessed` via Dapr state store.
- `pubsub.go`: Implements `Publish` to Dapr topic.
- Use mocked Dapr client in unit tests.

### Acceptance Criteria:
- Duplicates correctly detected (test with same ID).
- Messages published correctly (test payload is preserved).
- Errors from Dapr clients are propagated properly.

---

## ✅ Phase 6: Wire Everything with Container

**Goal:** Initialize the full system using the container pattern.

### Tasks:
- `internal/container/container.go`: wire config, logger, Dapr client, adapters.
- Register dependencies into the router/handler.

### Acceptance Criteria:
- Full flow works: config → middleware → handler → command → infra.
- Can inject mock adapters in tests.
- Service runs via `run-local.sh`.

---

## ✅ Phase 7: Local Development + Dapr Integration

**Goal:** Provide a reproducible local dev experience.

### Tasks:
- Implement `scripts/run-local.sh`:
  - Checks/install Dapr.
  - Spins up Docker deps (`redis`, `otel-collector`).
  - Launches Dapr + app together.
- Add `docker-compose.yml` for Redis and metrics stack.

### Acceptance Criteria:
- Local devs can run `scripts/run-local.sh` to start the whole stack.
- Logs show Dapr sidecar is attached and Redis is reachable.

---

## ✅ Phase 8: Integration & E2E Testing

**Goal:** Ensure behavior correctness across real interfaces.

### Tasks:
- `tests/integration/`: Mock Redis + verify handler → dedup + publish logic.
- `tests/e2e/`: Launch full system with Dapr, send requests to `/webhook`.

### Acceptance Criteria:
- Valid webhook reaches pubsub.
- Duplicate webhook returns 200 but is not published again.
- Malformed headers return 400.
- Rate-limited IPs blocked.

---

## ✅ Phase 9: Observability & Metrics

**Goal:** Expose health, logs, and metrics for monitoring.

### Tasks:
- `GET /healthz` endpoint.
- Prometheus/Grafana metrics via middleware or Gin plugin.
- Log structured request/response/error flows.

### Acceptance Criteria:
- Metrics available at `/metrics`.
- Logs contain timestamp, delivery ID, and errors.
- Health endpoint returns 200 if app + Redis + Dapr are alive.

---

## ✅ Phase 10: Security Hardening

**Goal:** Finalize WAF and signature verification.

### Tasks:
- Implement HMAC-based signature check in `validator.go`.
- Secure Dapr binding with component scopes (if needed).
- Audit logs for security-related events.

### Acceptance Criteria:
- Webhooks with invalid signature are rejected.
- Only known IPs can access `/webhook`.
- Rate limit + signature check tested in E2E.

---

## Summary Table

| Phase | Focus                     | Testable Outcome                          |
|-------|---------------------------|-------------------------------------------|
| 1     | Project structure, config | Config loaded, healthz OK                 |
| 2     | Middleware & routing      | Validation & WAF pass/fail                |
| 3     | Domain models             | Value object tests                        |
| 4     | Command handler           | Unit tests on handler                     |
| 5     | Dapr infra adapters       | Mocks pass pubsub/state tests             |
| 6     | Wire container            | Full path executes                        |
| 7     | Dev experience            | `run-local.sh` works                      |
| 8     | Integration/E2E          | System tests pass                         |
| 9     | Metrics/logging           | Prometheus/Grafana exports                |
| 10    | Security & signatures     | Valid/invalid signature tests             |
